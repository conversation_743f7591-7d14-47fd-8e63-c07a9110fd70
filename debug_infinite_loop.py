#!/usr/bin/env python3
"""Debug script to identify the infinite loop issue in ProductSearchFlow."""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Ensure OpenAI API key is available
openai_key = os.getenv("OPENAI_API_KEY")
if openai_key:
    os.environ["OPENAI_API_KEY"] = openai_key

from ecommerce_scraper.workflows.product_search_flow import ProductSearchFlow, ProductSearchState

def debug_infinite_loop():
    """Run a minimal test to debug the infinite loop issue."""
    print("🔍 Debugging infinite loop issue...")

    # Create initial state
    initial_state = ProductSearchState(
        product_query="Flow tDCS Headset",
        max_retailers=3,
        max_retries=3,
        session_id="debug-session"
    )

    print(f"Initial state - current_attempt: {initial_state.current_attempt}")
    print(f"Initial state - max_retries: {initial_state.max_retries}")

    try:
        # Start the flow with initial state
        flow = ProductSearchFlow(verbose=True)
        result = flow.kickoff(inputs=initial_state.model_dump())
        print(f"Flow completed with result: {result}")

    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        return
    except Exception as e:
        print(f"❌ Error during flow execution: {e}")
        import traceback
        traceback.print_exc()
        return

    # Print final state
    print(f"Final state - current_attempt: {flow.state.current_attempt}")
    print(f"Final state - validated_products: {len(flow.state.validated_products)}")

if __name__ == "__main__":
    debug_infinite_loop()
