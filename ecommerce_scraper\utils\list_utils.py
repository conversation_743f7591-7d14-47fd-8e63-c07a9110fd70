"""List/collection utility helpers for flows.

These helpers are intentionally simple and dependency-free to keep them safe to
use in agent/flow contexts without introducing additional side effects.
"""

from typing import Any, Dict, Iterable, List, Optional, Set, TypeVar
from urllib.parse import urlparse, urlunparse, parse_qsl, urlencode

T = TypeVar("T")


def dedupe_preserve_order(items: Iterable[T]) -> List[T]:
    """Return a new list with duplicates removed while preserving order."""
    seen: Set[Any] = set()
    out: List[T] = []
    for x in items:
        if x not in seen:
            seen.add(x)
            out.append(x)
    return out


def existing_urls(items: Iterable[Dict[str, Any]]) -> Set[str]:
    """Collect a set of URL strings from a list of dict objects.

    Items without a string `url` field are ignored.
    """
    urls: Set[str] = set()
    for item in items or []:
        try:
            url = (item or {}).get("url")
            if isinstance(url, str) and url:
                urls.add(url)
        except Exception:
            continue
    return urls
def _normalize_url_for_dedup(url: str) -> str:
    """Normalize URL for deduplication (case/fragment/trailing-slash, sorted query).

    Conservative to avoid over-merging different product variants:
    - Lowercase scheme and netloc
    - Drop URL fragment
    - Collapse a single trailing slash for non-root paths
    - Sort query parameters and drop common tracking params (utm_*, ref, gclid)
    """
    try:
        if not isinstance(url, str) or not url:
            return ""
        parsed = urlparse(url)
        scheme = (parsed.scheme or "").lower()
        netloc = (parsed.netloc or "").lower()
        path = parsed.path or ""
        # Remove a single trailing slash for non-root paths
        if path and path != "/" and path.endswith("/"):
            path = path[:-1]
        # Clean query: sort and remove tracking params
        query_pairs = parse_qsl(parsed.query, keep_blank_values=True)
        filtered_pairs = [
            (k, v)
            for k, v in query_pairs
            if not (k.lower().startswith("utm_") or k.lower() in {"gclid", "fbclid", "ref", "referrer"})
        ]
        query = urlencode(sorted(filtered_pairs)) if filtered_pairs else ""
        return urlunparse((scheme, netloc, path, "", query, ""))
    except Exception:
        return url



def append_unique_by_url(
    target: List[Dict[str, Any]],
    candidates: Iterable[Dict[str, Any]],
    *,
    max_items: Optional[int] = None,
) -> None:
    """Append candidate dicts to target when their `url` is unique.

    - Deduplicates against existing `target` URLs
    - If `max_items` is provided, stops once target reaches that length
    """
    existing = existing_urls(target)
    for cand in candidates or []:
        try:
            url = (cand or {}).get("url")
            if not isinstance(url, str) or not url:
                continue
            if url in existing:
                continue
            if max_items is not None and len(target) >= max_items:
                break
            target.append(cand)
            existing.add(url)
        except Exception:
            continue



def restore_product_fields_from_research(
    product: Dict[str, Any],
    research_item: Dict[str, Any],
) -> Dict[str, Any]:
    """Restore missing/placeholder product fields from a research item.

    Rules:
    - Backfill price when missing/empty or equals "Price unavailable" (case-insensitive)
    - Backfill retailer/name/url only when missing/empty
    - Backfill availability only when missing/empty
    - Never overwrite non-empty fields from the product
    """
    try:
        if not isinstance(product, dict):
            return product

        # Price backfill
        price_value = product.get("price")
        price_str = str(price_value).strip() if price_value is not None else ""
        if (not price_str) or (price_str.lower() == "price unavailable"):
            research_price = (research_item or {}).get("price")
            if isinstance(research_price, str) and research_price.strip():
                product["price"] = research_price.strip()

        # Retailer backfill
        retailer_value = product.get("retailer")
        if not isinstance(retailer_value, str) or not retailer_value.strip():
            research_retailer = (research_item or {}).get("vendor") or (research_item or {}).get("retailer")
            if isinstance(research_retailer, str) and research_retailer.strip():
                product["retailer"] = research_retailer.strip()

        # Name backfill (standardize on `name` only)
        name_value = product.get("name")
        if not isinstance(name_value, str) or not name_value.strip():
            research_name = (research_item or {}).get("name")
            if isinstance(research_name, str) and research_name.strip():
                product["name"] = research_name.strip()

        # URL backfill
        url_value = product.get("url")
        if not isinstance(url_value, str) or not url_value.strip():
            research_url = (research_item or {}).get("url")
            if isinstance(research_url, str) and research_url.strip():
                product["url"] = research_url.strip()

        # Availability backfill
        availability_value = product.get("availability")
        if not isinstance(availability_value, str) or not availability_value.strip():
            research_availability = (research_item or {}).get("availability")
            if isinstance(research_availability, str) and research_availability.strip():
                product["availability"] = research_availability.strip()

        return product
    except Exception:
        return product


def backfill_validated_product(
    validated_product: Dict[str, Any],
    confirmed_product: Optional[Dict[str, Any]] = None,
    retailers: Optional[List[Dict[str, Any]]] = None,
    current_retailer_index: int = 0,
) -> Dict[str, Any]:
    """Backfill fields on a validated product using confirmation and research data.

    Behavior:
    - If `validated_product.price` is empty or equals "Price unavailable" (case-insensitive),
      try to use `confirmed_product.price` when available.
    - If `validated_product.availability` is empty, try to use `confirmed_product.availability`,
      otherwise fall back to the current retailer entry's `availability` from `retailers`.

    The function is defensive and will return the original `validated_product` on errors.
    """
    try:
        if not isinstance(validated_product, dict):
            return validated_product

        # Backfill price when missing or marked unavailable
        price_value = validated_product.get("price")
        price_str = str(price_value).strip() if price_value is not None else ""
        if (not price_str) or (price_str.lower() == "price unavailable"):
            research_price = (confirmed_product or {}).get("price")
            if isinstance(research_price, str) and research_price.strip():
                validated_product["price"] = research_price.strip()

        # Backfill name when missing/empty
        name_value = validated_product.get("name")
        if not isinstance(name_value, str) or not name_value.strip():
            source_name = (confirmed_product or {}).get("name")
            if not isinstance(source_name, str) or not source_name.strip():
                try:
                    if (
                        isinstance(retailers, list)
                        and 0 <= current_retailer_index < len(retailers)
                        and isinstance(retailers[current_retailer_index], dict)
                    ):
                        source_name = (retailers[current_retailer_index] or {}).get("name")
                except Exception:
                    source_name = None
            validated_product["name"] = (source_name or "Unknown").strip()

        # Backfill availability when missing
        availability_value = validated_product.get("availability")
        if not isinstance(availability_value, str) or not availability_value.strip():
            source_availability = (confirmed_product or {}).get("availability")
            if not isinstance(source_availability, str) or not source_availability.strip():
                try:
                    if (
                        isinstance(retailers, list)
                        and 0 <= current_retailer_index < len(retailers)
                        and isinstance(retailers[current_retailer_index], dict)
                    ):
                        source_availability = (
                            retailers[current_retailer_index] or {}
                        ).get("availability")
                except Exception:
                    source_availability = None
            if isinstance(source_availability, str) and source_availability.strip():
                validated_product["availability"] = source_availability.strip()

        return validated_product
    except Exception:
        return validated_product


def append_priority_retailer_to_validated(
    validated_products: List[Dict[str, Any]],
    retailer_item: Dict[str, Any],
    *,
    product_query: Optional[str] = None,
) -> bool:
    """Append a priority retailer item directly into validated products.

    - De-duplicates by `url`
    - Maps fields from the retailer research result to validated product shape
    - Returns True if appended, False otherwise
    """
    try:
        if not isinstance(retailer_item, dict):
            return False
        url = (retailer_item or {}).get("url") or ""
        if not isinstance(url, str) or not url:
            return False
        existing = existing_urls(validated_products or [])
        if url in existing:
            return False
        retailer_name = (retailer_item or {}).get("vendor") or "Unknown"
        retailer_price = (retailer_item or {}).get("price") or ""
        retailer_product_name = (retailer_item or {}).get("name") or ""
        availability = (retailer_item or {}).get("availability") or ""
        validated_products.append(
            {
                "product_name": str(product_query or retailer_product_name or ""),
                "name": str(retailer_product_name or ""),
                "price": str(retailer_price or ""),
                "url": str(url or ""),
                "retailer": str(retailer_name or ""),
                "availability": str(availability or ""),
            }
        )
        return True
    except Exception:
        return False


def collect_urls_from_list(items: Iterable[Dict[str, Any]], *, key: str = "url") -> List[str]:
    """Collect URLs from a list of dict-like objects.

    - Ignores items without the specified key or with non-string/empty values
    - Returns URLs in the order encountered
    """
    urls: List[str] = []
    for item in items or []:
        try:
            url = (item or {}).get(key)
            if isinstance(url, str) and url:
                urls.append(url)
        except Exception:
            continue
    return urls


def deduplicate_urls(urls: Iterable[str]) -> List[str]:
    """De-duplicate a list of URL strings while preserving order using normalization."""
    seen_norm: Set[str] = set()
    out: List[str] = []
    for url in urls or []:
        try:
            if not isinstance(url, str) or not url:
                continue
            norm = _normalize_url_for_dedup(url)
            if norm in seen_norm:
                continue
            seen_norm.add(norm)
            out.append(url)
        except Exception:
            continue
    return out